<template>
  <el-dialog
    :title="title"
    class="dialog-scenc"
    width="80%"
    :modal-append-to-body="false"
    @close="handleClose"
    @open="handleOpen"
    :close-on-click-modal="false"
    destroy-on-close
    v-model="visible"
  >
    <div class="table-box">
      <div class="filter-container">
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'地市'"
          v-model="listQuery.city"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'区县'"
          v-model="listQuery.county"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'站址名称'"
          v-model="listQuery.siteName"
        >
        </el-input>
        <el-input
          @keyup.enter.native="handleFilter"
          size="mini"
          clearable
          style="width: 200px; margin-left: 8px"
          class="filter-item"
          :placeholder="'站址编码'"
          v-model="listQuery.siteCode"
        >
        </el-input>
        <el-button
          class="filter-item search-btn"
          style="margin-left: 8px"
          size="mini"
          @click="handleFilter"
          >搜索</el-button
        >
        <el-button
        class="filter-item search-btn"
        style="margin-left: 8px"
        size="mini"
        @click="handleExport"
        :loading="exportLoading"
        >导出</el-button>
      </div>
      <el-table
        ref="mainTable"
        height="510"
        border
        fit
        :data="tableList"
        v-loading="listLoading"
        style="width: 100%"
        :row-class-name="tableRowClassName"
      >
        <!-- 停电、欠压、退服、高温告警的列表 -->
        <template v-if="isAlarmType(['powerOutage', 'underVoltage', 'quitServer', 'wdgg'])">
          <el-table-column
            label="告警等级"
            min-width="120px"
            prop="alarmGrade"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="告警名称"
            min-width="150px"
            prop="alarmName"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="告警详情"
            min-width="200px"
            prop="alarmDetails"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="站址名称"
            min-width="150px"
            prop="siteName"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="站址运维ID"
            min-width="150px"
            prop="operationId"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="告警发生时间"
            min-width="180px"
            prop="alarmTime"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="告警历时（分钟）"
            min-width="150px"
            prop="alarmLast"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="站址编码"
            min-width="150px"
            prop="siteCode"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="所属运营商"
            min-width="120px"
            prop="operator"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="所属市"
            min-width="120px"
            prop="city"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="所属区县"
            min-width="120px"
            prop="county"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="告警流水号ID"
            min-width="150px"
            prop="alarmWaterId"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="是否超时"
            min-width="120px"
            prop="isTimeout"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="告警来源"
            min-width="120px"
            prop="alarmFrom"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="故障单编号"
            min-width="150px"
            prop="faultCode"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="创建时间"
            min-width="180px"
            prop="updateTime"
            align="center"
            show-overflow-tooltip
          />
        </template>

        <!-- 疑似退服、离线告警的列表 -->
        <template v-else-if="isAlarmType(['likeOutServer', 'outLine'])">
          <!-- <el-table-column
            label="省"
            min-width="100px"
            prop="province"
            align="center"
            show-overflow-tooltip
          /> -->
          <el-table-column
            label="市"
            min-width="120px"
            prop="city"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="区"
            min-width="120px"
            prop="region"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="站址"
            min-width="150px"
            prop="siteName"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="国家行政区县"
            min-width="150px"
            prop="county"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="站址运维ID"
            min-width="150px"
            prop="operationId"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="站址编码"
            min-width="150px"
            prop="siteCode"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="FSU名称"
            min-width="150px"
            prop="fsuName"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="FSU运维ID"
            min-width="150px"
            prop="fsuOperationId"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="代维公司"
            min-width="150px"
            prop="dwCompany"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="FSU软件厂家"
            min-width="150px"
            prop="fsuManufacturer"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="FSU硬件厂家"
            min-width="150px"
            prop="fsuHardware"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="负责人"
            min-width="120px"
            prop="head"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="维护人电话"
            min-width="150px"
            prop="headPhone"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="功率"
            min-width="100px"
            prop="power"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="离线时间"
            min-width="180px"
            prop="offlineTime"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="网络制式"
            min-width="120px"
            prop="networkStandard"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="FSU软件版本"
            min-width="150px"
            prop="fsuSoftware"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="设备型号"
            min-width="150px"
            prop="deviceModel"
            align="center"
            show-overflow-tooltip
          />
        </template>

        <!-- 烟雾/火灾告警、水浸的列表 -->
        <template v-else-if="isAlarmType(['fireSmoke', 'waterImmersion'])">
          <el-table-column
            label="故障等级"
            min-width="120px"
            prop="faultGrade"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="故障名称"
            min-width="150px"
            prop="faultName"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="故障详情"
            min-width="200px"
            prop="faultDetails"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="站址名称"
            min-width="150px"
            prop="siteName"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="故障发生时间"
            min-width="180px"
            prop="faultAlarmTime"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="故障历时（分钟）"
            min-width="150px"
            prop="faultLast"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="站址资源编码"
            min-width="150px"
            prop="siteCode"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="站址运维ID"
            min-width="150px"
            prop="operationId"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="所属市"
            min-width="120px"
            prop="city"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="所属区"
            min-width="120px"
            prop="county"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="故障流水ID"
            min-width="150px"
            prop="faultWaterId"
            align="center"
            show-overflow-tooltip
          />
        </template>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNum"
      :limit.sync="listQuery.pageSize"
      @pagination="handleCurrentChange"
    />
  </el-dialog>
</template>

<script>
import {
  getProvinceAlarmActiveList,
  getProvinceAlarmFsuList,
  getProvinceAlarmPyAlarmList,
  exportProvinceAlarmActiveList,
  exportProvinceAlarmFsuList,
  exportProvinceAlarmPyAlarmList
} from "@/api/sceneView/index";

export default {
  name: "modalTable",
  data() {
    return {
      title: "",
      visible: false,
      total: 0,
      listLoading: false,
      tableList: [],
      alarmType: "", // 当前告警类型
      listQuery: {
        city: "",              // 地市
        county: "",            // 区县
        siteName: "",          // 站址名称
        siteCode: "",          // 站址编码
        type: "",              // 运营商类型
        pageNum: 1,            // 页码
        pageSize: 20,          // 每页数量
      },
      exportLoading: false,
      // 中文到英文告警类型映射
      chineseToEnglishMap: {
        '停电': 'powerOutage',
        '欠压': 'underVoltage',
        '退服': 'quitServer',
        '疑似退服': 'likeOutServer',
        '离线': 'outLine',
        '温度过高': 'wdgg',
        '烟雾/火灾': 'fireSmoke',
        '水浸': 'waterImmersion'
      },
      // 告警类型映射
      alarmTypeMap: {
        fireSmoke: '烟雾/火灾告警',
        likeOutServer: '疑似退服告警',
        outLine: '离线告警',
        powerOutage: '停电告警',
        quitServer: '退服告警',
        underVoltage: '欠压告警',
        waterImmersion: '水浸告警',
        wdgg: '温度过高告警'
      }
    };
  },
  created() {},
  computed: {},
  watch: {},
  methods: {
    // 判断当前告警类型是否在指定类型列表中
    isAlarmType(types) {
      return types.includes(this.alarmType);
    },
    // 根据告警类型获取对应的API函数
    getApiFunction() {
      if (this.isAlarmType(['powerOutage', 'underVoltage', 'quitServer', 'wdgg'])) {
        return getProvinceAlarmPyAlarmList;
      } else if (this.isAlarmType(['likeOutServer', 'outLine'])) {
        return getProvinceAlarmFsuList;
      } else if (this.isAlarmType(['fireSmoke', 'waterImmersion'])) {
        return getProvinceAlarmActiveList;
      }
      return null;
    },

    // 根据告警类型获取对应的导出API函数
    getExportApiFunction() {
      if (this.isAlarmType(['powerOutage', 'underVoltage', 'quitServer', 'wdgg'])) {
        return exportProvinceAlarmPyAlarmList;
      } else if (this.isAlarmType(['likeOutServer', 'outLine'])) {
        return exportProvinceAlarmFsuList;
      } else if (this.isAlarmType(['fireSmoke', 'waterImmersion'])) {
        return exportProvinceAlarmActiveList;
      }
      return null;
    },

    async handleExport(){
      this.exportLoading = true;
      try {
        const exportApiFunction = this.getExportApiFunction();
        if (!exportApiFunction) {
          this.$message.error('不支持的告警类型');
          return;
        }

        const blob = await exportApiFunction({
          ...this.listQuery,
          pageSize: this.total
        });

        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.setAttribute('download', `${this.alarmTypeMap[this.alarmType] || '告警'}详情.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(link.href);
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败');
      }
      this.exportLoading = false;
    },


    handleOpen() {},
    handleCurrentChange(val) {
      this.listQuery.pageNum = val.page;
      this.listQuery.pageSize = val.limit;
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      try {
        const apiFunction = this.getApiFunction();
        if (!apiFunction) {
          this.$message.error('不支持的告警类型');
          this.listLoading = false;
          return;
        }

        const result = await apiFunction(this.listQuery);

        // 处理不同API返回格式
        if (result && result.list) {
          this.total = result.total || 0;
          this.tableList = result.list;
        } else if (Array.isArray(result)) {
          this.total = result.length;
          this.tableList = result;
        } else {
          this.total = 0;
          this.tableList = [];
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        this.$message.error('获取数据失败');
        this.total = 0;
        this.tableList = [];
      }
      this.listLoading = false;
    },

    initForm(data) {
      // 转换中文告警类型为英文
      const englishAlarmType = this.chineseToEnglishMap[data.alarmType] || data.alarmType;
      this.alarmType = englishAlarmType;
      this.title = `${this.alarmTypeMap[englishAlarmType] || data.alarmType + '告警'}详情列表`;

      this.listQuery = {
        city: data.city || "",
        county: "",
        siteName: "",
        siteCode: "",
        type: data.type || "",
        alarmType: data.alarmType,
        pageNum: 1,
        pageSize: 20,
      };

      this.visible = true;
      this.getList();
    },

    handleFilter() {
      this.listQuery.pageNum = 1;
      this.getList();
    },

    close() {
      this.alarmType = "";
      this.listQuery = {
        city: "",
        county: "",
        siteName: "",
        siteCode: "",
        type: "",
        pageNum: 1,
        pageSize: 20,
      };
      this.visible = false;
    },

    tableRowClassName({ rowIndex }) {
      return rowIndex % 2 != 0 ? "rowStyle" : "";
    },

    handleClose() {
      this.close();
    },
  },
};
</script>

<style scoped lang="scss">
.filter-container {
  text-align: right;
  padding: 8px 10px;
  background-color: #446f86;
  .filter-item {
    margin-bottom: 0;
  }
}
:deep() .el-input__wrapper {
  background-color: transparent;
  width: 100%;
  &.is-focus {
    box-shadow: 0 0 0 1px #059ec0;
  }
}
:deep() .el-input__inner {
  background-color: transparent;
  border: 0px solid #1296db;
  color: #82bee9;
}
.search-btn {
  background: #059ec0;
  color: #fff;
  border: 1px solid #059ec0;
  margin-top: -3px;
}
</style>

<style lang="scss">
.dialog-scenc {
  background-color: #065e89;
  opacity: 0.9;
  .el-dialog__header {
    height: 55px;
    line-height: 55px;
    border-radius: 4px 4px 0 0;
    border-bottom: 1px solid #059ec0;
    overflow: hidden;
    padding: 0 20px !important;
    .el-dialog__title {
      color: #fff;
    }
  }
  .el-dialog__body {
    padding: 10px 20px 0 20px;
    .rowStyle {
      background: #084969 !important;
    }
    .pagination-container {
      background: transparent;
      border-left: none;
      border-right: none;
      display: flex;
      justify-content: flex-end;
      .el-pagination__total {
        color: #fff;
      }
      .el-pagination__jump {
        color: #fff;
      }
    }
    .table-box {
      .sticky {
        background-color: rgba(144, 147, 153, 0.5);
        .el-input__inner {
          background: linear-gradient(0deg, #385fb866, #2238690d);
          border: 2px solid #059ec0;
          color: #82bee9;
          margin-bottom: 6px;
        }
        .search-btn {
          background: #059ec0;
          color: #fff;
          border: 1px solid #059ec0;
          margin-top: -3px;
        }
      }
      .el-table--enable-row-hover
        .el-table__body
        tr:hover
        > td.el-table__cell {
        background-color: rgba(133, 210, 249, 0.23922) !important;
      }
      .el-table td.el-table__cell {
        border-bottom: 1px solid #059ec0;
        color: #fff;
        height: 45px;
        font-size: 16px;
      }
      .el-table tr {
        background-color: transparent;
        height: 45px;
      }
      .el-table {
        background-color: transparent;
        &::before {
          height: 0;
        }
      }
      .el-table th.el-table__cell {
        background: #084969;
        color: #d2e7ff;
        font-size: 17px;
        font-weight: 700;
        border-bottom: 1px solid #059ec0;
      }
      .el-table__empty-text {
        color: #fff;
      }
      .el-table--border {
        border: 1px solid #059ec0;
      }
      .el-table--border .el-table__cell {
        border-right: 1px solid #059ec0;
      }
      .el-table--border::after {
        width: 0;
      }
      .el-table__body-wrapper {
        .el-table__body {
          width: 100% !important;
        }
        &::-webkit-scrollbar {
          width: 6px;
          height: 12px;
        }
        &::-webkit-scrollbar-thumb {
          // border-radius: 6px;
          background: rgba(144, 147, 153, 0.5);
          border-radius: 0;
          -webkit-box-shadow: inset 0 0 5px #0003;
        }
        &::-webkit-scrollbar-track {
          background: transparent;
          -webkit-box-shadow: inset 0 0 5px #0003;
          border-radius: 0;
        }
      }
    }
  }
}
</style>
